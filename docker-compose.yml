version: '3.8'

services:
  # Redis for Celery
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # FastAPI Backend
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./oci_inventory.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./oci_inventory.db:/app/oci_inventory.db
      - ./exports:/app/exports
      - ~/.oci:/root/.oci:ro  # Mount OCI config
    depends_on:
      - redis
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build: .
    command: celery -A app.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=sqlite:///./oci_inventory.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./oci_inventory.db:/app/oci_inventory.db
      - ./exports:/app/exports
      - ~/.oci:/root/.oci:ro  # Mount OCI config
    depends_on:
      - redis
    restart: unless-stopped

  # Celery Beat (Scheduler)
  celery-beat:
    build: .
    command: celery -A app.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=sqlite:///./oci_inventory.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./oci_inventory.db:/app/oci_inventory.db
      - ~/.oci:/root/.oci:ro  # Mount OCI config
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis_data:
