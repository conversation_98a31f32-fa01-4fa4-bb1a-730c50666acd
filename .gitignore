# ===================================
# OCI Inventory Dashboard - .gitignore
# ===================================

# ===== SECURITY & CREDENTIALS =====
# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OCI Configuration files
.oci/
*.pem
*.key
oci_config
config

# API Keys and certificates
*.p12
*.pfx
*.crt
*.cer
*.der

# ===== PYTHON =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# ===== DATABASE =====
# SQLite databases
*.db
*.sqlite
*.sqlite3
oci_inventory.db
oci_inventory.db-journal

# Database backups
*.sql
*.dump

# ===== CELERY =====
# Celery beat schedule file
celerybeat-schedule
celerybeat.pid

# Celery worker state
celery-worker.state

# ===== LOGS =====
# Log files
*.log
logs/
log/

# ===== EXPORTS & TEMPORARY FILES =====
# Export directory
exports/
*.xlsx
*.csv
*.json
*.xml

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.swp
*.swo
*~

# ===== NODE.JS / REACT =====
# Dependencies
frontend/node_modules/
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
frontend/build/
frontend/dist/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
.tmp
.temp

# ===== DOCKER =====
# Docker files (if using containers)
.dockerignore
docker-compose.override.yml

# ===== IDE & EDITORS =====
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== MACOS =====
# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# ===== WINDOWS =====
# Windows thumbnail cache files
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp

# Windows shortcuts
*.lnk

# ===== LINUX =====
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# ===== PROJECT SPECIFIC =====
# Test output files
test_output/
test_results/

# Backup files
*.bak
*.backup

# Configuration backups
config.bak
settings.bak

# Development scripts output
dev_output/

# Local development overrides
local_settings.py
local_config.py

# ===== DOCUMENTATION =====
# Sphinx documentation
docs/_build/

# mkdocs documentation
/site

# ===== MISC =====
# Thumbnails
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.ico

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# ===== KEEP THESE EXAMPLES =====
# Keep example files but ignore actual config
!.env.example
!config.example.py
!docker-compose.example.yml
