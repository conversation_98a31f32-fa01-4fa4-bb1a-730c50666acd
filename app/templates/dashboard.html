<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCI Inventory Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <h1 class="text-3xl font-bold text-gray-900">OCI Inventory Dashboard</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Refresh Data
                    </button>
                    <button id="scanBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Start Scan
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <span class="text-white font-bold">R</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Resources</dt>
                                <dd id="totalResources" class="text-lg font-medium text-gray-900">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <span class="text-white font-bold">C</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Compartments</dt>
                                <dd id="totalCompartments" class="text-lg font-medium text-gray-900">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                <span class="text-white font-bold">R</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Regions</dt>
                                <dd id="totalRegions" class="text-lg font-medium text-gray-900">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <span class="text-white font-bold">S</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Last Scan</dt>
                                <dd id="lastScan" class="text-sm font-medium text-gray-900">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Resources by Type</h3>
                <canvas id="resourceTypeChart" width="400" height="200"></canvas>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Resources by Environment</h3>
                <canvas id="environmentChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Environment</label>
                    <select id="environmentFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <option value="">All Environments</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Resource Type</label>
                    <select id="resourceTypeFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <option value="">All Types</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Region</label>
                    <select id="regionFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <option value="">All Regions</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" id="searchFilter" placeholder="Search resources..." 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
            </div>
            <div class="mt-4">
                <button id="applyFilters" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Apply Filters
                </button>
                <button id="clearFilters" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Clear Filters
                </button>
                <button id="exportBtn" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded">
                    Export to Excel
                </button>
            </div>
        </div>

        <!-- Resources Table -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Resources</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">List of all OCI resources</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Public IP</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VCN</th>
                        </tr>
                    </thead>
                    <tbody id="resourcesTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Resources will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        // Global variables
        let allResources = [];
        let resourceTypeChart = null;
        let environmentChart = null;

        // API base URL
        const API_BASE = '/api/v1';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('refreshBtn').addEventListener('click', loadDashboard);
            document.getElementById('scanBtn').addEventListener('click', startScan);
            document.getElementById('applyFilters').addEventListener('click', applyFilters);
            document.getElementById('clearFilters').addEventListener('click', clearFilters);
            document.getElementById('exportBtn').addEventListener('click', exportData);
        }

        async function loadDashboard() {
            try {
                // Load stats
                const statsResponse = await axios.get(`${API_BASE}/dashboard/stats`);
                updateStats(statsResponse.data);

                // Load resources
                const resourcesResponse = await axios.get(`${API_BASE}/resources`);
                allResources = resourcesResponse.data;
                updateResourcesTable(allResources);

                // Load filter options
                await loadFilterOptions();

                // Update charts
                updateCharts(statsResponse.data);

            } catch (error) {
                console.error('Error loading dashboard:', error);
                alert('Error loading dashboard data. Please check if the API is running.');
            }
        }

        function updateStats(stats) {
            document.getElementById('totalResources').textContent = stats.total_resources;
            document.getElementById('totalCompartments').textContent = stats.total_compartments;
            document.getElementById('totalRegions').textContent = stats.total_regions;
            
            const lastScan = stats.last_scan ? new Date(stats.last_scan).toLocaleString() : 'Never';
            document.getElementById('lastScan').textContent = lastScan;
        }

        function updateResourcesTable(resources) {
            const tbody = document.getElementById('resourcesTableBody');
            tbody.innerHTML = '';

            resources.forEach(resource => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${resource.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${resource.resource_type}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${resource.environment || 'N/A'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${resource.region_name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${resource.public_ip || 'N/A'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${resource.vcn_name || 'N/A'}</td>
                `;
                tbody.appendChild(row);
            });
        }

        async function loadFilterOptions() {
            try {
                // Load environments
                const envResponse = await axios.get(`${API_BASE}/filters/values?field=environment`);
                populateSelect('environmentFilter', envResponse.data.values);

                // Load resource types
                const typeResponse = await axios.get(`${API_BASE}/filters/values?field=resource_type`);
                populateSelect('resourceTypeFilter', typeResponse.data.values);

                // Load regions
                const regionResponse = await axios.get(`${API_BASE}/filters/values?field=region_name`);
                populateSelect('regionFilter', regionResponse.data.values);

            } catch (error) {
                console.error('Error loading filter options:', error);
            }
        }

        function populateSelect(selectId, options) {
            const select = document.getElementById(selectId);
            const currentValue = select.value;
            
            // Clear existing options except the first one
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });

            select.value = currentValue;
        }

        function updateCharts(stats) {
            // Resource Type Chart
            if (resourceTypeChart) {
                resourceTypeChart.destroy();
            }
            
            const typeCtx = document.getElementById('resourceTypeChart').getContext('2d');
            resourceTypeChart = new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(stats.resources_by_type),
                    datasets: [{
                        data: Object.values(stats.resources_by_type),
                        backgroundColor: [
                            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Environment Chart
            if (environmentChart) {
                environmentChart.destroy();
            }
            
            const envCtx = document.getElementById('environmentChart').getContext('2d');
            environmentChart = new Chart(envCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(stats.resources_by_environment),
                    datasets: [{
                        label: 'Resources',
                        data: Object.values(stats.resources_by_environment),
                        backgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        async function startScan() {
            try {
                const response = await axios.post(`${API_BASE}/inventory/scan`);
                alert('Inventory scan started! The dashboard will update automatically when complete.');
                
                // Poll for scan status
                pollScanStatus();
            } catch (error) {
                console.error('Error starting scan:', error);
                alert('Error starting scan. Please try again.');
            }
        }

        async function pollScanStatus() {
            try {
                const response = await axios.get(`${API_BASE}/inventory/scan/status`);
                const status = response.data.status;
                
                if (status === 'running') {
                    setTimeout(pollScanStatus, 5000); // Poll every 5 seconds
                } else if (status === 'completed') {
                    alert('Scan completed successfully!');
                    loadDashboard(); // Refresh the dashboard
                } else if (status === 'failed') {
                    alert('Scan failed. Please check the logs.');
                }
            } catch (error) {
                console.error('Error polling scan status:', error);
            }
        }

        async function applyFilters() {
            const environment = document.getElementById('environmentFilter').value;
            const resourceType = document.getElementById('resourceTypeFilter').value;
            const region = document.getElementById('regionFilter').value;
            const search = document.getElementById('searchFilter').value;

            const params = new URLSearchParams();
            if (environment) params.append('environment', environment);
            if (resourceType) params.append('resource_type', resourceType);
            if (region) params.append('region_name', region);
            if (search) params.append('search', search);

            try {
                const response = await axios.get(`${API_BASE}/resources?${params.toString()}`);
                updateResourcesTable(response.data);
            } catch (error) {
                console.error('Error applying filters:', error);
                alert('Error applying filters. Please try again.');
            }
        }

        function clearFilters() {
            document.getElementById('environmentFilter').value = '';
            document.getElementById('resourceTypeFilter').value = '';
            document.getElementById('regionFilter').value = '';
            document.getElementById('searchFilter').value = '';
            updateResourcesTable(allResources);
        }

        async function exportData() {
            const environment = document.getElementById('environmentFilter').value;
            const resourceType = document.getElementById('resourceTypeFilter').value;
            const region = document.getElementById('regionFilter').value;

            const params = new URLSearchParams();
            if (environment) params.append('environment', environment);
            if (resourceType) params.append('resource_type', resourceType);
            if (region) params.append('region_name', region);

            try {
                const response = await axios.get(`${API_BASE}/export/excel?${params.toString()}`);
                alert('Export completed! File saved to: ' + response.data.file_path);
            } catch (error) {
                console.error('Error exporting data:', error);
                alert('Error exporting data. Please try again.');
            }
        }
    </script>
</body>
</html>
