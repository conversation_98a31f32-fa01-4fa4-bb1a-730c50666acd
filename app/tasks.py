"""
Celery tasks for background processing
"""
import logging
from celery import current_task
from app.celery_app import celery_app
from app.services.inventory_service import InventoryService
from app.database import SessionLocal
from app import crud, schemas

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def run_inventory_scan(self, scan_id: int = None):
    """Run inventory scan as a Celery task"""
    try:
        # Create scan record if not provided
        if scan_id is None:
            db = SessionLocal()
            scan = crud.create_inventory_scan(db, schemas.InventoryScanCreate())
            scan_id = scan.id
            db.close()
        
        # Update task state
        self.update_state(
            state="PROGRESS",
            meta={"scan_id": scan_id, "status": "starting"}
        )
        
        # Run the scan
        inventory_service = InventoryService()
        inventory_service.run_full_scan(scan_id)
        
        return {
            "scan_id": scan_id,
            "status": "completed",
            "message": "Inventory scan completed successfully"
        }
        
    except Exception as e:
        logger.error(f"Inventory scan task failed: {e}")
        self.update_state(
            state="FAILURE",
            meta={"scan_id": scan_id, "error": str(e)}
        )
        raise


@celery_app.task
def run_scheduled_inventory_scan():
    """Run scheduled inventory scan"""
    logger.info("Running scheduled inventory scan")
    
    db = SessionLocal()
    try:
        # Check if there's already a running scan
        latest_scan = crud.get_latest_scan(db)
        if latest_scan and latest_scan.status == "running":
            logger.info("Skipping scheduled scan - another scan is already running")
            return {"status": "skipped", "reason": "scan_already_running"}
        
        # Create new scan
        scan = crud.create_inventory_scan(db, schemas.InventoryScanCreate())
        
        # Run the scan
        inventory_service = InventoryService()
        inventory_service.run_full_scan(scan.id)
        
        return {"status": "completed", "scan_id": scan.id}
        
    except Exception as e:
        logger.error(f"Scheduled inventory scan failed: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task
def export_data_task(filters: dict = None):
    """Export data as a background task"""
    try:
        from app.services.export_service import ExportService
        from app import schemas
        
        db = SessionLocal()
        export_service = ExportService()
        
        # Convert filters dict to ResourceFilter if provided
        resource_filters = None
        if filters:
            resource_filters = schemas.ResourceFilter(**filters)
        
        file_path = export_service.export_to_excel(db, resource_filters)
        db.close()
        
        return {
            "status": "completed",
            "file_path": file_path,
            "message": "Export completed successfully"
        }
        
    except Exception as e:
        logger.error(f"Export task failed: {e}")
        return {"status": "failed", "error": str(e)}


@celery_app.task
def cleanup_old_exports():
    """Clean up old export files"""
    import os
    import time
    from datetime import datetime, timedelta
    
    try:
        export_dir = "exports"
        if not os.path.exists(export_dir):
            return {"status": "completed", "message": "No export directory found"}
        
        # Delete files older than 7 days
        cutoff_time = time.time() - (7 * 24 * 60 * 60)
        deleted_files = 0
        
        for filename in os.listdir(export_dir):
            file_path = os.path.join(export_dir, filename)
            if os.path.isfile(file_path):
                file_time = os.path.getmtime(file_path)
                if file_time < cutoff_time:
                    os.remove(file_path)
                    deleted_files += 1
        
        return {
            "status": "completed",
            "deleted_files": deleted_files,
            "message": f"Cleaned up {deleted_files} old export files"
        }
        
    except Exception as e:
        logger.error(f"Cleanup task failed: {e}")
        return {"status": "failed", "error": str(e)}
