"""
Configuration settings for the OCI Inventory Dashboard
"""
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    # Application
    app_name: str = "OCI Inventory Dashboard"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Database
    database_url: str = "sqlite:///./oci_inventory.db"
    
    # Redis/Celery
    redis_url: str = "redis://localhost:6379/0"
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    
    # OCI Configuration
    oci_config_profile: str = "DEFAULT"
    oci_config_file: str = "~/.oci/config"
    
    # Tags for resource categorization
    environment_tag_key: str = "Environment"
    purpose_tag_key: str = "Purpose"
    
    # Data refresh settings
    auto_refresh_interval_minutes: int = 60
    
    # Security
    secret_key: str = "your-secret-key-change-this-in-production"
    access_token_expire_minutes: int = 30
    
    # CORS
    allowed_origins: list = ["http://localhost:3000", "http://localhost:8000"]
    
    class Config:
        env_file = ".env"


# Global settings instance
settings = Settings()
