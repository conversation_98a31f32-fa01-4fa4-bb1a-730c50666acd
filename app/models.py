"""
SQLAlchemy models for OCI resources
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class Compartment(Base):
    __tablename__ = "compartments"
    
    id = Column(String, primary_key=True)  # OCI compartment ID
    name = Column(String, nullable=False)
    description = Column(Text)
    lifecycle_state = Column(String)
    time_created = Column(DateTime)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    resources = relationship("Resource", back_populates="compartment")


class Region(Base):
    __tablename__ = "regions"
    
    name = Column(String, primary_key=True)  # e.g., "us-phoenix-1"
    display_name = Column(String)
    is_home_region = Column(Boolean, default=False)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    resources = relationship("Resource", back_populates="region")


class Resource(Base):
    __tablename__ = "resources"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    oci_id = Column(String, unique=True, nullable=False)  # OCI resource ID
    name = Column(String, nullable=False)
    resource_type = Column(String, nullable=False)  # Compute Instance, OKE Cluster, etc.
    
    # Location
    compartment_id = Column(String, ForeignKey("compartments.id"))
    region_name = Column(String, ForeignKey("regions.name"))
    
    # Network information
    vcn_name = Column(String)
    subnet_name = Column(String)
    private_ip = Column(String)
    public_ip = Column(String)
    
    # Resource-specific details
    shape = Column(String)
    node_count = Column(Integer)
    parent_cluster = Column(String)
    cidr_block = Column(String)
    
    # Tags and categorization
    environment = Column(String)
    purpose = Column(String)
    tags = Column(Text)  # JSON string of all tags
    
    # Lifecycle
    lifecycle_state = Column(String)
    time_created = Column(DateTime)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Cost information (for future use)
    estimated_monthly_cost = Column(Float)
    
    # Relationships
    compartment = relationship("Compartment", back_populates="resources")
    region = relationship("Region", back_populates="resources")


class InventoryScan(Base):
    __tablename__ = "inventory_scans"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    scan_started = Column(DateTime, default=func.now())
    scan_completed = Column(DateTime)
    status = Column(String, default="running")  # running, completed, failed
    total_resources = Column(Integer, default=0)
    total_compartments = Column(Integer, default=0)
    total_regions = Column(Integer, default=0)
    error_message = Column(Text)
    
    # Performance metrics
    scan_duration_seconds = Column(Float)


class NetworkTopology(Base):
    __tablename__ = "network_topology"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    oci_id = Column(String, unique=True, nullable=False)
    name = Column(String, nullable=False)
    resource_type = Column(String, nullable=False)  # VCN, Subnet
    
    # Location
    compartment_id = Column(String, ForeignKey("compartments.id"))
    region_name = Column(String, ForeignKey("regions.name"))
    
    # Network details
    cidr_block = Column(String)
    vcn_name = Column(String)  # For subnets, reference to parent VCN
    
    # Tags
    environment = Column(String)
    purpose = Column(String)
    tags = Column(Text)
    
    # Lifecycle
    time_created = Column(DateTime)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
