"""
Pydantic schemas for API request/response models
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ResourceBase(BaseModel):
    name: str
    resource_type: str
    compartment_id: str
    region_name: str
    environment: Optional[str] = None
    purpose: Optional[str] = None


class ResourceCreate(ResourceBase):
    oci_id: str
    vcn_name: Optional[str] = None
    subnet_name: Optional[str] = None
    private_ip: Optional[str] = None
    public_ip: Optional[str] = None
    shape: Optional[str] = None
    node_count: Optional[int] = None
    parent_cluster: Optional[str] = None
    cidr_block: Optional[str] = None
    lifecycle_state: Optional[str] = None
    time_created: Optional[datetime] = None
    tags: Optional[str] = None


class Resource(ResourceBase):
    id: int
    oci_id: str
    vcn_name: Optional[str] = None
    subnet_name: Optional[str] = None
    private_ip: Optional[str] = None
    public_ip: Optional[str] = None
    shape: Optional[str] = None
    node_count: Optional[int] = None
    parent_cluster: Optional[str] = None
    cidr_block: Optional[str] = None
    lifecycle_state: Optional[str] = None
    time_created: Optional[datetime] = None
    last_updated: datetime
    estimated_monthly_cost: Optional[float] = None
    
    class Config:
        from_attributes = True


class CompartmentBase(BaseModel):
    name: str
    description: Optional[str] = None


class CompartmentCreate(CompartmentBase):
    id: str
    lifecycle_state: Optional[str] = None
    time_created: Optional[datetime] = None


class Compartment(CompartmentBase):
    id: str
    lifecycle_state: Optional[str] = None
    time_created: Optional[datetime] = None
    last_updated: datetime
    
    class Config:
        from_attributes = True


class RegionBase(BaseModel):
    name: str
    display_name: Optional[str] = None


class RegionCreate(RegionBase):
    is_home_region: bool = False


class Region(RegionBase):
    is_home_region: bool = False
    last_updated: datetime
    
    class Config:
        from_attributes = True


class InventoryScanBase(BaseModel):
    status: str = "running"


class InventoryScanCreate(InventoryScanBase):
    pass


class InventoryScan(InventoryScanBase):
    id: int
    scan_started: datetime
    scan_completed: Optional[datetime] = None
    total_resources: int = 0
    total_compartments: int = 0
    total_regions: int = 0
    error_message: Optional[str] = None
    scan_duration_seconds: Optional[float] = None
    
    class Config:
        from_attributes = True


class DashboardStats(BaseModel):
    total_resources: int
    total_compartments: int
    total_regions: int
    resources_by_type: Dict[str, int]
    resources_by_environment: Dict[str, int]
    resources_by_region: Dict[str, int]
    last_scan: Optional[datetime] = None


class ResourceFilter(BaseModel):
    compartment_id: Optional[str] = None
    region_name: Optional[str] = None
    environment: Optional[str] = None
    resource_type: Optional[str] = None
    search: Optional[str] = None


class NetworkTopologyBase(BaseModel):
    name: str
    resource_type: str
    compartment_id: str
    region_name: str


class NetworkTopologyCreate(NetworkTopologyBase):
    oci_id: str
    cidr_block: Optional[str] = None
    vcn_name: Optional[str] = None
    environment: Optional[str] = None
    purpose: Optional[str] = None
    tags: Optional[str] = None
    time_created: Optional[datetime] = None


class NetworkTopology(NetworkTopologyBase):
    id: int
    oci_id: str
    cidr_block: Optional[str] = None
    vcn_name: Optional[str] = None
    environment: Optional[str] = None
    purpose: Optional[str] = None
    time_created: Optional[datetime] = None
    last_updated: datetime
    
    class Config:
        from_attributes = True
