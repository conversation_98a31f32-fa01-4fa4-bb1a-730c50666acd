"""
Inventory Service - Orchestrates the OCI resource scanning process
"""
import logging
import time
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.services.oci_service import OCIService
from app import crud, schemas, models
from app.config import settings

logger = logging.getLogger(__name__)


class InventoryService:
    def __init__(self):
        self.oci_service = OCIService()
    
    def run_full_scan(self, scan_id: int):
        """Run a complete inventory scan of all OCI resources"""
        db = SessionLocal()
        start_time = time.time()
        
        try:
            logger.info(f"Starting inventory scan {scan_id}")
            
            # Update scan status
            crud.update_inventory_scan(db, scan_id, status="running")
            
            # Get compartments and regions
            compartments = self.oci_service.get_all_compartments()
            regions = self.oci_service.get_subscribed_regions()
            
            if not compartments:
                raise Exception("No compartments found")
            if not regions:
                raise Exception("No regions found")
            
            # Clear existing data for fresh scan
            crud.clear_all_resources(db)
            
            # Store compartments and regions
            self._store_compartments(db, compartments)
            self._store_regions(db, regions)
            
            # Scan all resources
            total_resources = 0
            for compartment in compartments:
                for region in regions:
                    logger.info(f"Scanning compartment '{compartment.name}' in region '{region}'")
                    resources = self.oci_service.process_compartment_resources(compartment, region)
                    
                    # Store resources and network topology
                    resource_count = self._store_resources(db, resources)
                    self._store_network_topology(db, resources)
                    
                    total_resources += resource_count
                    logger.info(f"Found {resource_count} resources in '{compartment.name}' ({region})")
            
            # Calculate scan duration
            end_time = time.time()
            duration = end_time - start_time
            
            # Update scan completion
            crud.update_inventory_scan(
                db, scan_id,
                status="completed",
                scan_completed=datetime.utcnow(),
                total_resources=total_resources,
                total_compartments=len(compartments),
                total_regions=len(regions),
                scan_duration_seconds=duration
            )
            
            logger.info(f"Inventory scan {scan_id} completed successfully. "
                       f"Found {total_resources} resources in {duration:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Inventory scan {scan_id} failed: {e}")
            crud.update_inventory_scan(
                db, scan_id,
                status="failed",
                error_message=str(e),
                scan_completed=datetime.utcnow()
            )
        finally:
            db.close()
    
    def _store_compartments(self, db: Session, compartments: List[Any]):
        """Store compartments in database"""
        for compartment in compartments:
            compartment_data = schemas.CompartmentCreate(
                id=compartment.id,
                name=compartment.name,
                description=compartment.description,
                lifecycle_state=compartment.lifecycle_state,
                time_created=compartment.time_created
            )
            crud.create_compartment(db, compartment_data)
    
    def _store_regions(self, db: Session, regions: List[str]):
        """Store regions in database"""
        for region_name in regions:
            region_data = schemas.RegionCreate(
                name=region_name,
                display_name=region_name,  # Could be enhanced with proper display names
                is_home_region=False  # Could be enhanced to detect home region
            )
            crud.create_region(db, region_data)
    
    def _store_resources(self, db: Session, resources: List[Dict[str, Any]]) -> int:
        """Store resources in database"""
        count = 0
        for resource_data in resources:
            # Skip VCN and Subnet resources (they go to network topology)
            if resource_data.get("resource_type") in ["VCN", "Subnet"]:
                continue
            
            try:
                resource = schemas.ResourceCreate(**resource_data)
                crud.create_resource(db, resource)
                count += 1
            except Exception as e:
                logger.warning(f"Failed to store resource {resource_data.get('name', 'unknown')}: {e}")
        
        return count
    
    def _store_network_topology(self, db: Session, resources: List[Dict[str, Any]]):
        """Store network topology data"""
        for resource_data in resources:
            # Only store VCN and Subnet resources in network topology
            if resource_data.get("resource_type") not in ["VCN", "Subnet"]:
                continue
            
            try:
                network_data = schemas.NetworkTopologyCreate(
                    oci_id=resource_data["oci_id"],
                    name=resource_data["name"],
                    resource_type=resource_data["resource_type"],
                    compartment_id=resource_data["compartment_id"],
                    region_name=resource_data["region_name"],
                    cidr_block=resource_data.get("cidr_block"),
                    vcn_name=resource_data.get("vcn_name"),
                    environment=resource_data.get("environment"),
                    purpose=resource_data.get("purpose"),
                    tags=resource_data.get("tags"),
                    time_created=resource_data.get("time_created")
                )
                crud.create_network_topology(db, network_data)
            except Exception as e:
                logger.warning(f"Failed to store network topology {resource_data.get('name', 'unknown')}: {e}")
    
    def get_scan_progress(self, scan_id: int) -> Dict[str, Any]:
        """Get the progress of a running scan"""
        db = SessionLocal()
        try:
            scan = db.query(models.InventoryScan).filter(models.InventoryScan.id == scan_id).first()
            if not scan:
                return {"error": "Scan not found"}
            
            return {
                "scan_id": scan.id,
                "status": scan.status,
                "scan_started": scan.scan_started,
                "scan_completed": scan.scan_completed,
                "total_resources": scan.total_resources,
                "error_message": scan.error_message
            }
        finally:
            db.close()
    
    def schedule_auto_refresh(self):
        """Schedule automatic inventory refresh (for Celery)"""
        # This would be implemented with Celery periodic tasks
        pass
