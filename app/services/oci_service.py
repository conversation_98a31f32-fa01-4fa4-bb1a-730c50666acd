"""
OCI Service - Handles all OCI API interactions
Refactored from the original oci_inventory_exporter.py
"""
import oci
import logging
from typing import List, Dict, Any, Tuple
from oci.config import from_file
from app.config import settings

logger = logging.getLogger(__name__)


class OCIService:
    def __init__(self, config_profile: str = None):
        """Initialize OCI service with configuration"""
        self.config_profile = config_profile or settings.oci_config_profile
        self.config = None
        self.tenancy_id = None
        self.identity_client = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize OCI configuration and clients"""
        try:
            self.config = from_file(profile_name=self.config_profile)
            self.tenancy_id = self.config["tenancy"]
            self.identity_client = oci.identity.IdentityClient(self.config)
            logger.info(f"OCI service initialized with profile: {self.config_profile}")
        except Exception as e:
            logger.error(f"Failed to initialize OCI service: {e}")
            raise
    
    def get_all_compartments(self) -> List[Any]:
        """Fetch all active compartments in the tenancy"""
        logger.info("Fetching all compartments...")
        try:
            compartments = oci.pagination.list_call_get_all_results(
                self.identity_client.list_compartments,
                self.tenancy_id,
                compartment_id_in_subtree=True,
                access_level="ANY",
                lifecycle_state=oci.identity.models.Compartment.LIFECYCLE_STATE_ACTIVE
            ).data
            
            # Add root compartment
            root_compartment = self.identity_client.get_compartment(self.tenancy_id).data
            compartments.append(root_compartment)
            
            logger.info(f"Found {len(compartments)} active compartments")
            return compartments
        except oci.exceptions.ServiceError as e:
            logger.error(f"Failed to fetch compartments: {e}")
            return []
    
    def get_subscribed_regions(self) -> List[str]:
        """Fetch all regions the tenancy is subscribed to"""
        logger.info("Fetching subscribed regions...")
        try:
            regions = self.identity_client.list_region_subscriptions(self.tenancy_id).data
            region_names = [region.region_name for region in regions]
            logger.info(f"Found {len(region_names)} subscribed regions: {region_names}")
            return region_names
        except oci.exceptions.ServiceError as e:
            logger.error(f"Failed to fetch subscribed regions: {e}")
            return [self.config.get('region', 'us-phoenix-1')]
    
    def get_resource_tags(self, resource) -> Dict[str, str]:
        """Extract both freeform and defined tags from a resource"""
        tags = {}
        if hasattr(resource, 'freeform_tags') and resource.freeform_tags:
            tags.update(resource.freeform_tags)
        if hasattr(resource, 'defined_tags') and resource.defined_tags:
            for namespace, tag_set in resource.defined_tags.items():
                for key, value in tag_set.items():
                    tags[f"{namespace}.{key}"] = value
        return tags
    
    def process_compartment_resources(self, compartment, region_name: str) -> List[Dict[str, Any]]:
        """
        Fetch key resources and their relevant details for a compartment and region
        Refactored from original script
        """
        all_resources = []
        config = self.config.copy()
        config["region"] = region_name
        
        # Initialize clients for this region
        compute_client = oci.core.ComputeClient(config)
        network_client = oci.core.VirtualNetworkClient(config)
        load_balancer_client = oci.load_balancer.LoadBalancerClient(config)
        oke_client = oci.container_engine.ContainerEngineClient(config)
        mysql_client = oci.mysql.DbSystemClient(config)
        
        # Pre-fetch network layout for mapping
        vcn_map, subnet_map = self._get_network_mapping(network_client, compartment.id)
        
        # Collect all resource types
        all_resources.extend(self._get_vcns_and_subnets(network_client, compartment, region_name, vcn_map))
        all_resources.extend(self._get_compute_instances(compute_client, network_client, compartment, region_name, subnet_map))
        all_resources.extend(self._get_oke_clusters(oke_client, compartment, region_name, vcn_map))
        all_resources.extend(self._get_load_balancers(load_balancer_client, compartment, region_name))
        all_resources.extend(self._get_mysql_databases(mysql_client, compartment, region_name, subnet_map))
        
        return all_resources
    
    def _get_network_mapping(self, network_client, compartment_id: str) -> Tuple[Dict[str, str], Dict[str, Tuple[str, str]]]:
        """Pre-fetch network layout for mapping"""
        try:
            vcns_data = oci.pagination.list_call_get_all_results(
                network_client.list_vcns, compartment_id=compartment_id
            ).data
            subnets_data = oci.pagination.list_call_get_all_results(
                network_client.list_subnets, compartment_id=compartment_id
            ).data
            
            vcn_map = {v.id: v.display_name for v in vcns_data}
            subnet_map = {s.id: (s.display_name, vcn_map.get(s.vcn_id, "N/A")) for s in subnets_data}
            
            return vcn_map, subnet_map
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not pre-fetch network layout: {e}")
            return {}, {}
    
    def _get_vcns_and_subnets(self, network_client, compartment, region_name: str, vcn_map: Dict[str, str]) -> List[Dict[str, Any]]:
        """Get VCNs and Subnets"""
        resources = []
        try:
            # VCNs
            vcns_data = oci.pagination.list_call_get_all_results(
                network_client.list_vcns, compartment_id=compartment.id
            ).data
            for vcn in vcns_data:
                tags = self.get_resource_tags(vcn)
                resources.append({
                    "oci_id": vcn.id,
                    "resource_type": "VCN",
                    "name": vcn.display_name,
                    "cidr_block": vcn.cidr_block,
                    "environment": tags.get(settings.environment_tag_key, "N/A"),
                    "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                    "compartment_id": compartment.id,
                    "region_name": region_name,
                    "time_created": vcn.time_created,
                    "lifecycle_state": vcn.lifecycle_state,
                    "tags": str(tags)
                })
            
            # Subnets
            subnets_data = oci.pagination.list_call_get_all_results(
                network_client.list_subnets, compartment_id=compartment.id
            ).data
            for subnet in subnets_data:
                tags = self.get_resource_tags(subnet)
                resources.append({
                    "oci_id": subnet.id,
                    "resource_type": "Subnet",
                    "name": subnet.display_name,
                    "cidr_block": subnet.cidr_block,
                    "vcn_name": vcn_map.get(subnet.vcn_id, "N/A"),
                    "environment": tags.get(settings.environment_tag_key, "N/A"),
                    "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                    "compartment_id": compartment.id,
                    "region_name": region_name,
                    "time_created": subnet.time_created,
                    "lifecycle_state": subnet.lifecycle_state,
                    "tags": str(tags)
                })
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not fetch VCNs/Subnets in '{compartment.name}' ({region_name}): {e}")
        
        return resources
