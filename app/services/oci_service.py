"""
OCI Service - Handles all OCI API interactions
Refactored from the original oci_inventory_exporter.py
"""
import oci
import logging
from typing import List, Dict, Any, Tuple
from oci.config import from_file
from app.config import settings

logger = logging.getLogger(__name__)


class OCIService:
    def __init__(self, config_profile: str = None):
        """Initialize OCI service with configuration"""
        self.config_profile = config_profile or settings.oci_config_profile
        self.config = None
        self.tenancy_id = None
        self.identity_client = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize OCI configuration and clients"""
        try:
            self.config = from_file(profile_name=self.config_profile)
            self.tenancy_id = self.config["tenancy"]
            self.identity_client = oci.identity.IdentityClient(self.config)
            logger.info(f"OCI service initialized with profile: {self.config_profile}")
        except Exception as e:
            logger.error(f"Failed to initialize OCI service: {e}")
            raise
    
    def get_all_compartments(self) -> List[Any]:
        """Fetch all active compartments in the tenancy"""
        logger.info("Fetching all compartments...")
        try:
            compartments = oci.pagination.list_call_get_all_results(
                self.identity_client.list_compartments,
                self.tenancy_id,
                compartment_id_in_subtree=True,
                access_level="ANY",
                lifecycle_state=oci.identity.models.Compartment.LIFECYCLE_STATE_ACTIVE
            ).data
            
            # Add root compartment
            root_compartment = self.identity_client.get_compartment(self.tenancy_id).data
            compartments.append(root_compartment)
            
            logger.info(f"Found {len(compartments)} active compartments")
            return compartments
        except oci.exceptions.ServiceError as e:
            logger.error(f"Failed to fetch compartments: {e}")
            return []
    
    def get_subscribed_regions(self) -> List[str]:
        """Fetch all regions the tenancy is subscribed to"""
        logger.info("Fetching subscribed regions...")
        try:
            regions = self.identity_client.list_region_subscriptions(self.tenancy_id).data
            region_names = [region.region_name for region in regions]
            logger.info(f"Found {len(region_names)} subscribed regions: {region_names}")
            return region_names
        except oci.exceptions.ServiceError as e:
            logger.error(f"Failed to fetch subscribed regions: {e}")
            return [self.config.get('region', 'us-phoenix-1')]
    
    def get_resource_tags(self, resource) -> Dict[str, str]:
        """Extract both freeform and defined tags from a resource"""
        tags = {}
        if hasattr(resource, 'freeform_tags') and resource.freeform_tags:
            tags.update(resource.freeform_tags)
        if hasattr(resource, 'defined_tags') and resource.defined_tags:
            for namespace, tag_set in resource.defined_tags.items():
                for key, value in tag_set.items():
                    tags[f"{namespace}.{key}"] = value
        return tags
    
    def process_compartment_resources(self, compartment, region_name: str) -> List[Dict[str, Any]]:
        """
        Fetch key resources and their relevant details for a compartment and region
        Refactored from original script
        """
        all_resources = []
        config = self.config.copy()
        config["region"] = region_name
        
        # Initialize clients for this region
        compute_client = oci.core.ComputeClient(config)
        network_client = oci.core.VirtualNetworkClient(config)
        load_balancer_client = oci.load_balancer.LoadBalancerClient(config)
        oke_client = oci.container_engine.ContainerEngineClient(config)
        mysql_client = oci.mysql.DbSystemClient(config)
        
        # Pre-fetch network layout for mapping
        vcn_map, subnet_map = self._get_network_mapping(network_client, compartment.id)
        
        # Collect all resource types
        all_resources.extend(self._get_vcns_and_subnets(network_client, compartment, region_name, vcn_map))
        all_resources.extend(self._get_compute_instances(compute_client, network_client, compartment, region_name, subnet_map))
        all_resources.extend(self._get_oke_clusters(oke_client, compartment, region_name, vcn_map))
        all_resources.extend(self._get_load_balancers(load_balancer_client, compartment, region_name))
        all_resources.extend(self._get_mysql_databases(mysql_client, compartment, region_name, subnet_map))
        
        return all_resources
    
    def _get_network_mapping(self, network_client, compartment_id: str) -> Tuple[Dict[str, str], Dict[str, Tuple[str, str]]]:
        """Pre-fetch network layout for mapping"""
        try:
            vcns_data = oci.pagination.list_call_get_all_results(
                network_client.list_vcns, compartment_id=compartment_id
            ).data
            subnets_data = oci.pagination.list_call_get_all_results(
                network_client.list_subnets, compartment_id=compartment_id
            ).data
            
            vcn_map = {v.id: v.display_name for v in vcns_data}
            subnet_map = {s.id: (s.display_name, vcn_map.get(s.vcn_id, "N/A")) for s in subnets_data}
            
            return vcn_map, subnet_map
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not pre-fetch network layout: {e}")
            return {}, {}
    
    def _get_vcns_and_subnets(self, network_client, compartment, region_name: str, vcn_map: Dict[str, str]) -> List[Dict[str, Any]]:
        """Get VCNs and Subnets"""
        resources = []
        try:
            # VCNs
            vcns_data = oci.pagination.list_call_get_all_results(
                network_client.list_vcns, compartment_id=compartment.id
            ).data
            for vcn in vcns_data:
                tags = self.get_resource_tags(vcn)
                resources.append({
                    "oci_id": vcn.id,
                    "resource_type": "VCN",
                    "name": vcn.display_name,
                    "cidr_block": vcn.cidr_block,
                    "environment": tags.get(settings.environment_tag_key, "N/A"),
                    "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                    "compartment_id": compartment.id,
                    "region_name": region_name,
                    "time_created": vcn.time_created,
                    "lifecycle_state": vcn.lifecycle_state,
                    "tags": str(tags)
                })
            
            # Subnets
            subnets_data = oci.pagination.list_call_get_all_results(
                network_client.list_subnets, compartment_id=compartment.id
            ).data
            for subnet in subnets_data:
                tags = self.get_resource_tags(subnet)
                resources.append({
                    "oci_id": subnet.id,
                    "resource_type": "Subnet",
                    "name": subnet.display_name,
                    "cidr_block": subnet.cidr_block,
                    "vcn_name": vcn_map.get(subnet.vcn_id, "N/A"),
                    "environment": tags.get(settings.environment_tag_key, "N/A"),
                    "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                    "compartment_id": compartment.id,
                    "region_name": region_name,
                    "time_created": subnet.time_created,
                    "lifecycle_state": subnet.lifecycle_state,
                    "tags": str(tags)
                })
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not fetch VCNs/Subnets in '{compartment.name}' ({region_name}): {e}")
        
        return resources

    def _get_compute_instances(self, compute_client, network_client, compartment, region_name: str, subnet_map: Dict[str, Tuple[str, str]]) -> List[Dict[str, Any]]:
        """Get Compute Instances (VMs)"""
        resources = []
        try:
            logger.info(f"Fetching Compute Instances in '{compartment.name}' ({region_name})")
            instances = oci.pagination.list_call_get_all_results(
                compute_client.list_instances, compartment_id=compartment.id
            ).data

            for instance in instances:
                tags = self.get_resource_tags(instance)
                resource_data = {
                    "oci_id": instance.id,
                    "resource_type": "Compute Instance",
                    "name": instance.display_name,
                    "shape": instance.shape,
                    "environment": tags.get(settings.environment_tag_key, "N/A"),
                    "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                    "compartment_id": compartment.id,
                    "region_name": region_name,
                    "time_created": instance.time_created,
                    "lifecycle_state": instance.lifecycle_state,
                    "tags": str(tags)
                }

                # Get network information
                try:
                    vnic_attachments = oci.pagination.list_call_get_all_results(
                        compute_client.list_vnic_attachments,
                        compartment_id=compartment.id,
                        instance_id=instance.id
                    ).data
                    if vnic_attachments:
                        vnic = network_client.get_vnic(vnic_attachments[0].vnic_id).data
                        resource_data["private_ip"] = vnic.private_ip or "N/A"
                        resource_data["public_ip"] = vnic.public_ip or "N/A"
                        subnet_info = subnet_map.get(vnic.subnet_id, ("N/A", "N/A"))
                        resource_data["subnet_name"] = subnet_info[0]
                        resource_data["vcn_name"] = subnet_info[1]
                except oci.exceptions.ServiceError:
                    resource_data["private_ip"] = "Error"
                    resource_data["public_ip"] = "Error"

                resources.append(resource_data)
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not fetch Instances in '{compartment.name}' ({region_name}): {e}")

        return resources

    def _get_oke_clusters(self, oke_client, compartment, region_name: str, vcn_map: Dict[str, str]) -> List[Dict[str, Any]]:
        """Get OKE Clusters and Node Pools"""
        resources = []
        try:
            logger.info(f"Fetching OKE Clusters in '{compartment.name}' ({region_name})")
            clusters = oci.pagination.list_call_get_all_results(
                oke_client.list_clusters, compartment_id=compartment.id
            ).data

            for cluster in clusters:
                tags = self.get_resource_tags(cluster)
                resources.append({
                    "oci_id": cluster.id,
                    "resource_type": "OKE Cluster",
                    "name": cluster.name,
                    "vcn_name": vcn_map.get(cluster.vcn_id, "N/A"),
                    "environment": tags.get(settings.environment_tag_key, "N/A"),
                    "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                    "compartment_id": compartment.id,
                    "region_name": region_name,
                    "time_created": cluster.time_created,
                    "lifecycle_state": cluster.lifecycle_state,
                    "tags": str(tags)
                })

                # Get node pools
                node_pools = oci.pagination.list_call_get_all_results(
                    oke_client.list_node_pools,
                    compartment_id=compartment.id,
                    cluster_id=cluster.id
                ).data
                for pool in node_pools:
                    pool_tags = self.get_resource_tags(pool)
                    resources.append({
                        "oci_id": pool.id,
                        "resource_type": "OKE Node Pool",
                        "name": pool.name,
                        "shape": pool.node_shape,
                        "node_count": pool.node_config_details.size,
                        "parent_cluster": cluster.name,
                        "environment": pool_tags.get(settings.environment_tag_key, "N/A"),
                        "purpose": pool_tags.get(settings.purpose_tag_key, "N/A"),
                        "compartment_id": compartment.id,
                        "region_name": region_name,
                        "time_created": pool.time_created,
                        "lifecycle_state": pool.lifecycle_state,
                        "tags": str(pool_tags)
                    })
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not fetch OKE data in '{compartment.name}' ({region_name}): {e}")

        return resources

    def _get_load_balancers(self, load_balancer_client, compartment, region_name: str) -> List[Dict[str, Any]]:
        """Get Load Balancers"""
        resources = []
        try:
            logger.info(f"Fetching Load Balancers in '{compartment.name}' ({region_name})")
            lbs = oci.pagination.list_call_get_all_results(
                load_balancer_client.list_load_balancers, compartment_id=compartment.id
            ).data

            for lb in lbs:
                tags = self.get_resource_tags(lb)
                public_ips = [ip.ip_address for ip in lb.ip_addresses if ip.is_public]
                resources.append({
                    "oci_id": lb.id,
                    "resource_type": "Load Balancer",
                    "name": lb.display_name,
                    "shape": lb.shape_name,
                    "public_ip": ', '.join(public_ips) if public_ips else "N/A",
                    "environment": tags.get(settings.environment_tag_key, "N/A"),
                    "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                    "compartment_id": compartment.id,
                    "region_name": region_name,
                    "time_created": lb.time_created,
                    "lifecycle_state": lb.lifecycle_state,
                    "tags": str(tags)
                })
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not fetch Load Balancers in '{compartment.name}' ({region_name}): {e}")

        return resources

    def _get_mysql_databases(self, mysql_client, compartment, region_name: str, subnet_map: Dict[str, Tuple[str, str]]) -> List[Dict[str, Any]]:
        """Get MySQL Database Systems"""
        resources = []
        try:
            logger.info(f"Fetching MySQL Databases in '{compartment.name}' ({region_name})")
            db_summaries = oci.pagination.list_call_get_all_results(
                mysql_client.list_db_systems, compartment_id=compartment.id
            ).data

            for db_summary in db_summaries:
                try:
                    # Get full details which includes subnet_id
                    db = mysql_client.get_db_system(db_system_id=db_summary.id).data
                    tags = self.get_resource_tags(db)
                    db_type = "MySQL HeatWave DB" if db.is_heat_wave_cluster_attached else "MySQL DB System"
                    subnet_info = subnet_map.get(db.subnet_id, ("N/A", "N/A"))

                    resources.append({
                        "oci_id": db.id,
                        "resource_type": db_type,
                        "name": db.display_name,
                        "shape": db.shape_name,
                        "vcn_name": subnet_info[1],
                        "subnet_name": subnet_info[0],
                        "environment": tags.get(settings.environment_tag_key, "N/A"),
                        "purpose": tags.get(settings.purpose_tag_key, "N/A"),
                        "compartment_id": compartment.id,
                        "region_name": region_name,
                        "time_created": db.time_created,
                        "lifecycle_state": db.lifecycle_state,
                        "tags": str(tags)
                    })
                except oci.exceptions.ServiceError as detail_e:
                    logger.error(f"Could not get full details for DB '{db_summary.display_name}': {detail_e}")
        except oci.exceptions.ServiceError as e:
            logger.warning(f"Could not fetch MySQL DBs in '{compartment.name}' ({region_name}): {e}")

        return resources
