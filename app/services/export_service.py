"""
Export Service - Handles data export functionality
"""
import pandas as pd
import os
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session
from app import crud, schemas


class ExportService:
    def __init__(self):
        self.export_dir = "exports"
        os.makedirs(self.export_dir, exist_ok=True)
    
    def export_to_excel(self, db: Session, filters: Optional[schemas.ResourceFilter] = None) -> str:
        """Export resources to Excel file"""
        # Get resources and network topology
        resources = crud.get_resources(db, limit=10000, filters=filters)
        network_topology = crud.get_network_topology(db)
        compartments = crud.get_compartments(db)
        
        # Create compartment mapping
        compartment_map = {c.id: c.name for c in compartments}
        
        # Convert resources to DataFrame
        resources_data = []
        for resource in resources:
            resources_data.append({
                "Compartment Name": compartment_map.get(resource.compartment_id, "Unknown"),
                "Region": resource.region_name,
                "Environment": resource.environment,
                "Resource Type": resource.resource_type,
                "Name": resource.name,
                "Purpose": resource.purpose,
                "Shape": resource.shape,
                "Public IP": resource.public_ip,
                "Private IP": resource.private_ip,
                "Node Count": resource.node_count,
                "Parent Cluster": resource.parent_cluster,
                "VCN Name": resource.vcn_name,
                "Subnet Name": resource.subnet_name,
                "Lifecycle State": resource.lifecycle_state,
                "Time Created": resource.time_created,
                "Last Updated": resource.last_updated
            })
        
        # Convert network topology to DataFrame
        network_data = []
        for network in network_topology:
            network_data.append({
                "Compartment Name": compartment_map.get(network.compartment_id, "Unknown"),
                "Region": network.region_name,
                "Environment": network.environment,
                "Resource Type": network.resource_type,
                "Name": network.name,
                "Purpose": network.purpose,
                "CIDR Block": network.cidr_block,
                "VCN Name": network.vcn_name,
                "Time Created": network.time_created,
                "Last Updated": network.last_updated
            })
        
        # Create DataFrames
        resources_df = pd.DataFrame(resources_data)
        network_df = pd.DataFrame(network_data)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"oci_inventory_{timestamp}.xlsx"
        filepath = os.path.join(self.export_dir, filename)
        
        # Write to Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            resources_df.to_excel(writer, sheet_name='Master_Inventory', index=False)
            network_df.to_excel(writer, sheet_name='Network_Layout', index=False)
        
        return filepath
    
    def export_to_csv(self, db: Session, filters: Optional[schemas.ResourceFilter] = None) -> str:
        """Export resources to CSV file"""
        resources = crud.get_resources(db, limit=10000, filters=filters)
        compartments = crud.get_compartments(db)
        
        # Create compartment mapping
        compartment_map = {c.id: c.name for c in compartments}
        
        # Convert to DataFrame
        resources_data = []
        for resource in resources:
            resources_data.append({
                "Compartment Name": compartment_map.get(resource.compartment_id, "Unknown"),
                "Region": resource.region_name,
                "Environment": resource.environment,
                "Resource Type": resource.resource_type,
                "Name": resource.name,
                "Purpose": resource.purpose,
                "Shape": resource.shape,
                "Public IP": resource.public_ip,
                "Private IP": resource.private_ip,
                "VCN Name": resource.vcn_name,
                "Subnet Name": resource.subnet_name,
                "Lifecycle State": resource.lifecycle_state,
                "Time Created": resource.time_created
            })
        
        df = pd.DataFrame(resources_data)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"oci_inventory_{timestamp}.csv"
        filepath = os.path.join(self.export_dir, filename)
        
        # Write to CSV
        df.to_csv(filepath, index=False)
        
        return filepath
