# OCI Inventory Dashboard

A comprehensive web dashboard for Oracle Cloud Infrastructure (OCI) resource management that provides real-time visibility into your cloud infrastructure with advanced filtering, cost analysis, and management-friendly reporting.

## 🎯 Features

### Core Functionality
- **Real-time Resource Inventory**: Live view of all OCI resources across compartments and regions
- **Advanced Filtering**: Multi-dimensional filtering by Environment, Compartment, Region, Resource Type
- **Interactive Tables**: Sortable, searchable data tables with export capabilities
- **Network Topology View**: Visual representation of VCN/Subnet relationships
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile

### Resource Types Supported
- **Compute Instances**: VMs with network details and configurations
- **OKE Clusters**: Kubernetes clusters and node pools
- **Databases**: MySQL and HeatWave database systems
- **Load Balancers**: Public-facing load balancers with IP addresses
- **Network Components**: VCNs and Subnets with CIDR information

### Management Features
- **Executive Dashboard**: High-level KPIs and resource summaries
- **Export Capabilities**: Excel and CSV export with filtering
- **Background Scanning**: Automated resource discovery via Celery
- **Tag-based Organization**: Environment and Purpose tag categorization

## 🏗️ Architecture

### Backend Stack
- **FastAPI**: High-performance Python web framework
- **SQLAlchemy**: Database ORM with SQLite for development
- **Celery + Redis**: Background task processing and scheduling
- **OCI SDK**: Native Oracle Cloud Infrastructure integration

### Frontend Stack (Planned)
- **React**: Modern, responsive UI framework
- **Chart.js**: Beautiful charts and visualizations
- **Tailwind CSS**: Modern, professional styling
- **React Table**: Advanced table functionality

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Redis server
- Valid OCI configuration at `~/.oci/config`

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd oci_inventory_exporter
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start Redis** (if not already running)
   ```bash
   # macOS with Homebrew
   brew install redis && brew services start redis
   
   # Ubuntu/Debian
   sudo apt-get install redis-server && sudo systemctl start redis
   
   # Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

5. **Run the development environment**
   ```bash
   python run_dev.py
   ```

### Using Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📊 API Endpoints

### Dashboard
- `GET /api/v1/dashboard/stats` - Get dashboard statistics
- `GET /api/v1/resources` - Get resources with filtering
- `GET /api/v1/compartments` - Get all compartments
- `GET /api/v1/regions` - Get all regions

### Inventory Management
- `POST /api/v1/inventory/scan` - Trigger new inventory scan
- `GET /api/v1/inventory/scan/status` - Get scan status

### Export
- `GET /api/v1/export/excel` - Export to Excel with filtering

### Network
- `GET /api/v1/network-topology` - Get network topology data

## 🔧 Configuration

### Environment Variables

```bash
# Application
APP_NAME="OCI Inventory Dashboard"
DEBUG=false

# Database
DATABASE_URL="sqlite:///./oci_inventory.db"

# Redis/Celery
REDIS_URL="redis://localhost:6379/0"

# OCI Configuration
OCI_CONFIG_PROFILE="DEFAULT"
ENVIRONMENT_TAG_KEY="Environment"
PURPOSE_TAG_KEY="Purpose"

# Auto-refresh interval (minutes)
AUTO_REFRESH_INTERVAL_MINUTES=60
```

### OCI Configuration

Ensure your `~/.oci/config` file is properly configured:

```ini
[DEFAULT]
user=ocid1.user.oc1..your-user-id
fingerprint=your-fingerprint
key_file=~/.oci/oci_api_key.pem
tenancy=ocid1.tenancy.oc1..your-tenancy-id
region=us-phoenix-1
```

## 📈 Usage

### Running Your First Scan

1. **Access the dashboard**: http://localhost:8000
2. **View API documentation**: http://localhost:8000/docs
3. **Trigger a scan**: 
   ```bash
   curl -X POST http://localhost:8000/api/v1/inventory/scan
   ```
4. **Check scan status**:
   ```bash
   curl http://localhost:8000/api/v1/inventory/scan/status
   ```

### Filtering Resources

The API supports comprehensive filtering:

```bash
# Filter by environment and region
curl "http://localhost:8000/api/v1/resources?environment=prd&region_name=us-phoenix-1"

# Search across resource names
curl "http://localhost:8000/api/v1/resources?search=web-server"

# Filter by resource type
curl "http://localhost:8000/api/v1/resources?resource_type=Compute Instance"
```

### Exporting Data

```bash
# Export all resources
curl "http://localhost:8000/api/v1/export/excel"

# Export with filters
curl "http://localhost:8000/api/v1/export/excel?environment=prd&region_name=us-phoenix-1"
```

## 🔄 Background Tasks

The application uses Celery for background processing:

- **Automatic Scans**: Configurable interval scanning
- **Export Processing**: Large exports run in background
- **Cleanup Tasks**: Automatic cleanup of old export files

## 🛠️ Development

### Project Structure

```
oci_inventory_exporter/
├── app/
│   ├── api/                 # FastAPI routes
│   ├── services/            # Business logic
│   ├── models.py           # Database models
│   ├── schemas.py          # Pydantic schemas
│   ├── crud.py             # Database operations
│   └── main.py             # FastAPI application
├── frontend/               # React frontend (planned)
├── requirements.txt        # Python dependencies
├── docker-compose.yml      # Docker services
└── run_dev.py             # Development runner
```

### Adding New Resource Types

1. **Update OCI Service**: Add new resource collection method
2. **Update Models**: Add new fields if needed
3. **Update Schemas**: Add new Pydantic models
4. **Update CRUD**: Add new database operations
5. **Update API**: Add new endpoints if needed

## 🚀 Deployment

### Production Deployment

1. **Use PostgreSQL** instead of SQLite for production
2. **Set up proper Redis** cluster for high availability
3. **Configure environment variables** for production
4. **Use reverse proxy** (nginx) for static file serving
5. **Set up monitoring** and logging

### OCI Container Instances

The application is designed to run on OCI Container Instances:

```bash
# Build and push to OCI Container Registry
docker build -t oci-inventory-dashboard .
docker tag oci-inventory-dashboard <region>.ocir.io/<tenancy>/oci-inventory-dashboard:latest
docker push <region>.ocir.io/<tenancy>/oci-inventory-dashboard:latest
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the logs for error details
- Ensure OCI configuration is correct
- Verify Redis connectivity

## 🎯 Roadmap

### Phase 2: Cost Intelligence
- Real-time cost analysis
- Budget tracking and alerts
- Cost optimization recommendations

### Phase 3: Operational Intelligence
- Resource health monitoring
- Capacity planning
- Compliance dashboards

### Phase 4: Advanced Analytics
- Predictive scaling
- Multi-tenancy support
- Advanced reporting
