#!/usr/bin/env python3
"""
Development runner script
"""
import subprocess
import sys
import os
import time
from multiprocessing import Process

def run_fastapi():
    """Run FastAPI development server"""
    subprocess.run([
        sys.executable, "-m", "uvicorn", 
        "app.main:app", 
        "--reload", 
        "--host", "0.0.0.0", 
        "--port", "8000"
    ])

def run_celery_worker():
    """Run Celery worker"""
    subprocess.run([
        sys.executable, "-m", "celery", 
        "-A", "app.celery_app", 
        "worker", 
        "--loglevel=info"
    ])

def run_celery_beat():
    """Run Celery beat scheduler"""
    subprocess.run([
        sys.executable, "-m", "celery", 
        "-A", "app.celery_app", 
        "beat", 
        "--loglevel=info"
    ])

def check_redis():
    """Check if Redis is running"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        return True
    except:
        return False

def main():
    """Main development runner"""
    print("🚀 Starting OCI Inventory Dashboard Development Environment")
    
    # Check if Redis is running
    if not check_redis():
        print("❌ Redis is not running. Please start Redis first:")
        print("   brew install redis && brew services start redis  # macOS")
        print("   sudo apt-get install redis-server && sudo systemctl start redis  # Ubuntu")
        print("   docker run -d -p 6379:6379 redis:alpine  # Docker")
        sys.exit(1)
    
    print("✅ Redis is running")
    
    # Create necessary directories
    os.makedirs("exports", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # Start processes
    processes = []
    
    try:
        print("🌐 Starting FastAPI server...")
        fastapi_process = Process(target=run_fastapi)
        fastapi_process.start()
        processes.append(fastapi_process)
        
        time.sleep(2)  # Give FastAPI time to start
        
        print("👷 Starting Celery worker...")
        celery_worker_process = Process(target=run_celery_worker)
        celery_worker_process.start()
        processes.append(celery_worker_process)
        
        print("⏰ Starting Celery beat scheduler...")
        celery_beat_process = Process(target=run_celery_beat)
        celery_beat_process.start()
        processes.append(celery_beat_process)
        
        print("\n🎉 All services started successfully!")
        print("📊 Dashboard: http://localhost:8000")
        print("📚 API Docs: http://localhost:8000/docs")
        print("🔍 API Health: http://localhost:8000/health")
        print("\nPress Ctrl+C to stop all services")
        
        # Wait for all processes
        for process in processes:
            process.join()
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping all services...")
        for process in processes:
            process.terminate()
            process.join()
        print("✅ All services stopped")

if __name__ == "__main__":
    main()
