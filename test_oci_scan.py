#!/usr/bin/env python3
"""
Test script to verify OCI scanning works without errors
"""
import sys
import logging
from app.services.oci_service import OCIService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_oci_service():
    """Test OCI service functionality"""
    print("🧪 Testing OCI Service...")
    
    try:
        # Initialize OCI service
        oci_service = OCIService()
        print("✅ OCI service initialized successfully")
        
        # Test compartment access
        compartments = oci_service.get_all_compartments()
        print(f"✅ Found {len(compartments)} compartments")
        
        # Test region access
        regions = oci_service.get_subscribed_regions()
        print(f"✅ Found {len(regions)} regions")
        
        # Test resource scanning for first compartment and region
        if compartments and regions:
            test_compartment = compartments[0]
            test_region = regions[0]
            
            print(f"🔍 Testing resource scan for '{test_compartment.name}' in '{test_region}'...")
            
            try:
                resources = oci_service.process_compartment_resources(test_compartment, test_region)
                print(f"✅ Successfully scanned {len(resources)} resources")
                
                # Show resource types found
                resource_types = {}
                for resource in resources:
                    res_type = resource.get('resource_type', 'Unknown')
                    resource_types[res_type] = resource_types.get(res_type, 0) + 1
                
                if resource_types:
                    print("📊 Resource types found:")
                    for res_type, count in resource_types.items():
                        print(f"   - {res_type}: {count}")
                else:
                    print("ℹ️ No resources found in test compartment/region")
                
                return True
                
            except Exception as scan_error:
                print(f"❌ Resource scan failed: {scan_error}")
                return False
        else:
            print("⚠️ No compartments or regions found to test")
            return False
            
    except Exception as e:
        print(f"❌ OCI service test failed: {e}")
        return False

def test_safe_attribute_access():
    """Test the safe attribute access functionality"""
    print("\n🧪 Testing safe attribute access...")
    
    try:
        oci_service = OCIService()
        
        # Create a mock object to test safe attribute access
        class MockResource:
            def __init__(self):
                self.existing_attr = "test_value"
        
        mock_resource = MockResource()
        
        # Test existing attribute
        result1 = oci_service.safe_get_attribute(mock_resource, 'existing_attr')
        assert result1 == "test_value", "Should return existing attribute value"
        
        # Test non-existing attribute with default
        result2 = oci_service.safe_get_attribute(mock_resource, 'non_existing_attr', 'default')
        assert result2 == "default", "Should return default value for non-existing attribute"
        
        # Test non-existing attribute without default
        result3 = oci_service.safe_get_attribute(mock_resource, 'non_existing_attr')
        assert result3 is None, "Should return None for non-existing attribute without default"
        
        print("✅ Safe attribute access working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Safe attribute access test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 OCI Inventory Service Test")
    print("=" * 50)
    
    # Test safe attribute access
    safe_attr_test = test_safe_attribute_access()
    
    # Test OCI service
    oci_test = test_oci_service()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    tests = [
        ("Safe Attribute Access", safe_attr_test),
        ("OCI Service", oci_test)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! OCI service is working correctly.")
        print("\nYou can now run a full inventory scan:")
        print("  curl -X POST http://localhost:8000/api/v1/inventory/scan")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
