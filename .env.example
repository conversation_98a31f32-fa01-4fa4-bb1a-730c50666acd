# Application Configuration
APP_NAME="OCI Inventory Dashboard"
APP_VERSION="1.0.0"
DEBUG=false

# Database
DATABASE_URL="sqlite:///./oci_inventory.db"

# Redis/Celery
REDIS_URL="redis://localhost:6379/0"
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"

# OCI Configuration
OCI_CONFIG_PROFILE="DEFAULT"
OCI_CONFIG_FILE="~/.oci/config"

# Tags for resource categorization
ENVIRONMENT_TAG_KEY="Environment"
PURPOSE_TAG_KEY="Purpose"

# Data refresh settings (in minutes)
AUTO_REFRESH_INTERVAL_MINUTES=60

# Security
SECRET_KEY="your-secret-key-change-this-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000"]
