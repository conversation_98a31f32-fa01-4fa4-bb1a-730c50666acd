{"name": "oci-inventory-dashboard", "version": "1.0.0", "description": "OCI Inventory Dashboard Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.3.0", "axios": "^1.1.3", "chart.js": "^3.9.1", "react-chartjs-2": "^4.3.1", "react-table": "^7.8.0", "tailwindcss": "^3.1.8", "@headlessui/react": "^1.7.3", "@heroicons/react": "^2.0.12", "date-fns": "^2.29.3", "react-hot-toast": "^2.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.12", "postcss": "^8.4.16"}, "proxy": "http://localhost:8000"}