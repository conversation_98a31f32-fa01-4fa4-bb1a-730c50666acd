@tailwind base;
@tailwind components;
@tailwind utilities;

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom styles for the dashboard */
.stat-card {
  @apply bg-white overflow-hidden shadow rounded-lg;
}

.stat-card-content {
  @apply p-5;
}

.stat-icon {
  @apply w-8 h-8 rounded-md flex items-center justify-center text-white font-bold;
}

.filter-container {
  @apply bg-white p-6 rounded-lg shadow mb-6;
}

.table-container {
  @apply bg-white shadow overflow-hidden sm:rounded-md;
}

.table-header {
  @apply px-4 py-5 sm:px-6;
}

.table-responsive {
  @apply overflow-x-auto;
}

.table-main {
  @apply min-w-full divide-y divide-gray-200;
}

.table-head {
  @apply bg-gray-50;
}

.table-head-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm;
}

.table-cell-primary {
  @apply font-medium text-gray-900;
}

.table-cell-secondary {
  @apply text-gray-500;
}

/* Chart containers */
.chart-container {
  @apply bg-white p-6 rounded-lg shadow;
}

.chart-title {
  @apply text-lg font-medium text-gray-900 mb-4;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full border-b-2 border-blue-500;
}

/* Button styles */
.btn-primary {
  @apply bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded;
}

.btn-secondary {
  @apply bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded;
}

.btn-success {
  @apply bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded;
}

.btn-warning {
  @apply bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded;
}

/* Form elements */
.form-select {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500;
}

/* Status indicators */
.status-active {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800;
}

.status-inactive {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800;
}

.status-pending {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800;
}

/* Environment badges */
.env-prd {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800;
}

.env-stg {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800;
}

.env-test {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800;
}

.env-dev {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800;
}

/* Resource type icons */
.resource-icon {
  @apply w-6 h-6 mr-2 inline-block;
}
