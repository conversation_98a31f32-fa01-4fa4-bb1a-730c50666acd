import React, { useState, useEffect } from 'react';
import axios from 'axios';

const API_BASE = process.env.REACT_APP_API_URL || '/api/v1';

const ResourceTable = ({ resources: initialResources, onRefresh }) => {
  const [resources, setResources] = useState(initialResources || []);
  const [filteredResources, setFilteredResources] = useState(initialResources || []);
  const [filters, setFilters] = useState({
    environment: '',
    resourceType: '',
    region: '',
    search: ''
  });
  const [filterOptions, setFilterOptions] = useState({
    environments: [],
    resourceTypes: [],
    regions: []
  });
  const [loading, setLoading] = useState(false);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  useEffect(() => {
    setResources(initialResources || []);
    setFilteredResources(initialResources || []);
    loadFilterOptions();
  }, [initialResources]);

  useEffect(() => {
    applyFilters();
  }, [filters, resources]);

  const loadFilterOptions = async () => {
    try {
      const [envResponse, typeResponse, regionResponse] = await Promise.all([
        axios.get(`${API_BASE}/filters/values?field=environment`),
        axios.get(`${API_BASE}/filters/values?field=resource_type`),
        axios.get(`${API_BASE}/filters/values?field=region_name`)
      ]);

      setFilterOptions({
        environments: envResponse.data.values || [],
        resourceTypes: typeResponse.data.values || [],
        regions: regionResponse.data.values || []
      });
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  const applyFilters = () => {
    let filtered = [...resources];

    // Apply filters
    if (filters.environment) {
      filtered = filtered.filter(r => r.environment === filters.environment);
    }
    if (filters.resourceType) {
      filtered = filtered.filter(r => r.resource_type === filters.resourceType);
    }
    if (filters.region) {
      filtered = filtered.filter(r => r.region_name === filters.region);
    }
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(r => 
        r.name?.toLowerCase().includes(searchTerm) ||
        r.resource_type?.toLowerCase().includes(searchTerm) ||
        r.environment?.toLowerCase().includes(searchTerm) ||
        r.purpose?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply sorting
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        const aValue = a[sortConfig.key] || '';
        const bValue = b[sortConfig.key] || '';
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredResources(filtered);
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleFilterChange = (filterKey, value) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      environment: '',
      resourceType: '',
      region: '',
      search: ''
    });
  };

  const exportData = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.environment) params.append('environment', filters.environment);
      if (filters.resourceType) params.append('resource_type', filters.resourceType);
      if (filters.region) params.append('region_name', filters.region);

      const response = await axios.get(`${API_BASE}/export/excel?${params.toString()}`);
      alert('Export completed! File saved to: ' + response.data.file_path);
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Error exporting data. Please try again.');
    }
  };

  const getEnvironmentBadge = (environment) => {
    if (!environment || environment === 'N/A') return null;
    
    const envClass = {
      'prd': 'env-prd',
      'prod': 'env-prd',
      'production': 'env-prd',
      'stg': 'env-stg',
      'staging': 'env-stg',
      'test': 'env-test',
      'tst': 'env-test',
      'dev': 'env-dev',
      'development': 'env-dev'
    };

    const className = envClass[environment.toLowerCase()] || 'bg-gray-100 text-gray-800';
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${className}`}>
        {environment}
      </span>
    );
  };

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return <span className="text-gray-400">↕️</span>;
    }
    return sortConfig.direction === 'asc' ? <span>↑</span> : <span>↓</span>;
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="filter-container">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Environment</label>
            <select
              value={filters.environment}
              onChange={(e) => handleFilterChange('environment', e.target.value)}
              className="form-select"
            >
              <option value="">All Environments</option>
              {filterOptions.environments.map(env => (
                <option key={env} value={env}>{env}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Resource Type</label>
            <select
              value={filters.resourceType}
              onChange={(e) => handleFilterChange('resourceType', e.target.value)}
              className="form-select"
            >
              <option value="">All Types</option>
              {filterOptions.resourceTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Region</label>
            <select
              value={filters.region}
              onChange={(e) => handleFilterChange('region', e.target.value)}
              className="form-select"
            >
              <option value="">All Regions</option>
              {filterOptions.regions.map(region => (
                <option key={region} value={region}>{region}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search resources..."
              className="form-input"
            />
          </div>
        </div>
        
        <div className="mt-4 flex space-x-2">
          <button onClick={clearFilters} className="btn-secondary">
            Clear Filters
          </button>
          <button onClick={exportData} className="btn-warning">
            Export to Excel
          </button>
          <button onClick={onRefresh} className="btn-primary">
            Refresh Data
          </button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="bg-white p-4 rounded-lg shadow">
        <p className="text-sm text-gray-600">
          Showing {filteredResources.length} of {resources.length} resources
        </p>
      </div>

      {/* Resources Table */}
      <div className="table-container">
        <div className="table-header">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Resources</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Comprehensive list of all OCI resources
          </p>
        </div>
        
        <div className="table-responsive">
          <table className="table-main">
            <thead className="table-head">
              <tr>
                <th 
                  className="table-head-cell cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('name')}
                >
                  Name {getSortIcon('name')}
                </th>
                <th 
                  className="table-head-cell cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('resource_type')}
                >
                  Type {getSortIcon('resource_type')}
                </th>
                <th 
                  className="table-head-cell cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('environment')}
                >
                  Environment {getSortIcon('environment')}
                </th>
                <th 
                  className="table-head-cell cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('region_name')}
                >
                  Region {getSortIcon('region_name')}
                </th>
                <th className="table-head-cell">Public IP</th>
                <th className="table-head-cell">Private IP</th>
                <th className="table-head-cell">VCN</th>
                <th className="table-head-cell">Shape</th>
                <th className="table-head-cell">Purpose</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredResources.map((resource, index) => (
                <tr key={resource.id || index} className="hover:bg-gray-50">
                  <td className="table-cell table-cell-primary">
                    {resource.name}
                  </td>
                  <td className="table-cell table-cell-secondary">
                    {resource.resource_type}
                  </td>
                  <td className="table-cell">
                    {getEnvironmentBadge(resource.environment)}
                  </td>
                  <td className="table-cell table-cell-secondary">
                    {resource.region_name}
                  </td>
                  <td className="table-cell table-cell-secondary">
                    {resource.public_ip || 'N/A'}
                  </td>
                  <td className="table-cell table-cell-secondary">
                    {resource.private_ip || 'N/A'}
                  </td>
                  <td className="table-cell table-cell-secondary">
                    {resource.vcn_name || 'N/A'}
                  </td>
                  <td className="table-cell table-cell-secondary">
                    {resource.shape || 'N/A'}
                  </td>
                  <td className="table-cell table-cell-secondary">
                    {resource.purpose || 'N/A'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {filteredResources.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No resources found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResourceTable;
