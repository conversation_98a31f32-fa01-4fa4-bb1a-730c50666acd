import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Dashboard from './components/Dashboard';
import ResourceTable from './components/ResourceTable';
import NetworkTopology from './components/NetworkTopology';
import './App.css';

const API_BASE = process.env.REACT_APP_API_URL || '/api/v1';

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [dashboardStats, setDashboardStats] = useState(null);
  const [resources, setResources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load dashboard stats
      const statsResponse = await axios.get(`${API_BASE}/dashboard/stats`);
      setDashboardStats(statsResponse.data);

      // Load resources
      const resourcesResponse = await axios.get(`${API_BASE}/resources`);
      setResources(resourcesResponse.data);

    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load data. Please ensure the API is running.');
    } finally {
      setLoading(false);
    }
  };

  const triggerScan = async () => {
    try {
      await axios.post(`${API_BASE}/inventory/scan`);
      alert('Inventory scan started! The dashboard will update when complete.');
      
      // Poll for completion
      pollScanStatus();
    } catch (err) {
      console.error('Error starting scan:', err);
      alert('Failed to start scan. Please try again.');
    }
  };

  const pollScanStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE}/inventory/scan/status`);
      const status = response.data.status;
      
      if (status === 'running') {
        setTimeout(pollScanStatus, 5000);
      } else if (status === 'completed') {
        alert('Scan completed successfully!');
        loadInitialData();
      } else if (status === 'failed') {
        alert('Scan failed. Please check the logs.');
      }
    } catch (err) {
      console.error('Error polling scan status:', err);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading OCI Inventory...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Connection Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={loadInitialData}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">
                OCI Inventory Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={loadInitialData}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Refresh
              </button>
              <button
                onClick={triggerScan}
                className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
              >
                Start Scan
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setCurrentView('dashboard')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                currentView === 'dashboard'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setCurrentView('resources')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                currentView === 'resources'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Resources
            </button>
            <button
              onClick={() => setCurrentView('network')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                currentView === 'network'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Network Topology
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {currentView === 'dashboard' && (
          <Dashboard 
            stats={dashboardStats} 
            resources={resources}
            onRefresh={loadInitialData}
          />
        )}
        {currentView === 'resources' && (
          <ResourceTable 
            resources={resources}
            onRefresh={loadInitialData}
          />
        )}
        {currentView === 'network' && (
          <NetworkTopology onRefresh={loadInitialData} />
        )}
      </main>
    </div>
  );
}

export default App;
