#!/usr/bin/env python3
"""
Test script to verify the OCI Inventory Dashboard setup
"""
import sys
import os
import requests
import time
import json
from pathlib import Path

def test_imports():
    """Test that all required packages can be imported"""
    print("🧪 Testing Python imports...")
    
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import celery
        import redis
        import oci
        import pandas
        import pydantic
        print("✅ All Python packages imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_oci_config():
    """Test OCI configuration"""
    print("🔧 Testing OCI configuration...")
    
    try:
        from oci.config import from_file
        config = from_file(profile_name="DEFAULT")
        
        # Check required keys
        required_keys = ['user', 'fingerprint', 'key_file', 'tenancy', 'region']
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing OCI config key: {key}")
                return False
        
        # Check if key file exists
        key_file = os.path.expanduser(config['key_file'])
        if not os.path.exists(key_file):
            print(f"❌ OCI key file not found: {key_file}")
            return False
        
        print("✅ OCI configuration is valid")
        return True
    except Exception as e:
        print(f"❌ OCI configuration error: {e}")
        return False

def test_redis_connection():
    """Test Redis connection"""
    print("🔗 Testing Redis connection...")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis connection successful")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("   Please ensure Redis is running:")
        print("   - macOS: brew install redis && brew services start redis")
        print("   - Ubuntu: sudo apt-get install redis-server && sudo systemctl start redis")
        print("   - Docker: docker run -d -p 6379:6379 redis:alpine")
        return False

def test_database_setup():
    """Test database setup"""
    print("🗄️ Testing database setup...")
    
    try:
        from app.database import create_tables, engine
        from app.models import Base
        
        # Create tables
        create_tables()
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            assert result.fetchone()[0] == 1
        
        print("✅ Database setup successful")
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def test_api_startup():
    """Test API startup"""
    print("🌐 Testing API startup...")
    
    try:
        # Import the app
        from app.main import app
        print("✅ FastAPI app imported successfully")
        return True
    except Exception as e:
        print(f"❌ API startup failed: {e}")
        return False

def test_oci_service():
    """Test OCI service functionality"""
    print("☁️ Testing OCI service...")
    
    try:
        from app.services.oci_service import OCIService
        
        oci_service = OCIService()
        
        # Test basic functionality
        compartments = oci_service.get_all_compartments()
        regions = oci_service.get_subscribed_regions()
        
        print(f"✅ OCI service working - Found {len(compartments)} compartments and {len(regions)} regions")
        return True
    except Exception as e:
        print(f"❌ OCI service test failed: {e}")
        return False

def create_sample_env():
    """Create sample .env file if it doesn't exist"""
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 Creating sample .env file...")
        with open(".env.example", "r") as example:
            content = example.read()
        with open(".env", "w") as env:
            env.write(content)
        print("✅ Created .env file from example")

def main():
    """Run all tests"""
    print("🚀 OCI Inventory Dashboard Setup Test")
    print("=" * 50)
    
    # Create .env if needed
    create_sample_env()
    
    tests = [
        ("Python Imports", test_imports),
        ("OCI Configuration", test_oci_config),
        ("Redis Connection", test_redis_connection),
        ("Database Setup", test_database_setup),
        ("API Startup", test_api_startup),
        ("OCI Service", test_oci_service),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run: python run_dev.py")
        print("2. Open: http://localhost:8000")
        print("3. API Docs: http://localhost:8000/docs")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
